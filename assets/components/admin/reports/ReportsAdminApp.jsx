import { useEffect, useState, useContext } from 'react';
import axios from 'axios';
import AsyncSelect from 'react-select/async';
import debounce from 'debounce-promise';
import DatePicker from 'react-datepicker';
import { dateToDateString, getMonthsAgoDate } from 'utilities/graph-options';
import { apiReportsPath, apiAdminCustomersPath, apiAdminSuppliersPath } from 'routes';
import 'react-datepicker/dist/react-datepicker.css';
import appContext from 'contexts/appContext';

import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  LineController,
  BarController,
} from 'chart.js';

import ReportsGraph from './ReportsGraph';
import ReportDoughnut from './ReportDoughnut';
import Spinner from '../../checkout/Spinner';

ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>roller,
  Bar<PERSON><PERSON>roller
);

// Search Icon Component
const SearchIcon = () => (
  <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '8px', opacity: 0.6 }}>
    <path
      d="M8.059 15.11A7.06 7.06 0 0 1 1.007 8.06 7.06 7.06 0 0 1 8.06 1.007a7.06 7.06 0 0 1 7.052 7.052 7.06 7.06 0 0 1-7.052 7.052zM20 19.289l-5.907-5.906a8.016 8.016 0 0 0 2.025-5.323C16.118 3.615 12.503 0 8.06 0 3.615 0 0 3.615 0 8.059c0 4.444 3.615 8.06 8.059 8.06 2.04 0 3.902-.77 5.322-2.025L19.288 20l.712-.712z"
      fill="#666"
      fillRule="evenodd"
    />
  </svg>
);

const ReportsAdminApp = () => {
  const { isAdmin } = useContext(appContext);
  const [report, setReport] = useState({});
  const [reportData, setReportData] = useState([]);
  const [sourceType, setSourceType] = useState('CustomerProfile');
  const [reportType, setReportType] = useState('monthly');
  const [excludeStaffing, setExcludeStaffing] = useState(true);
  const [dates, setDates] = useState({ start: getMonthsAgoDate(isAdmin ? 1 : 5), end: getMonthsAgoDate(0) });
  const [loading, setLoading] = useState(true);
  const [activeCustomer, setActiveCustomer] = useState();
  const [activeSupplier, setActiveSupplier] = useState();

  function onDateChange(newDates) {
    const [start, end] = newDates;
    setDates({ start, end });
  }

  useEffect(async () => {
    if (!dates.start || !dates.end) return;

    setLoading(true);
    const { data } = await axios({
      method: 'GET',
      url: apiReportsPath({ format: 'json' }),
      params: {
        ...(sourceType === 'CustomerProfile' && { source_type: sourceType }),
        ...(sourceType === 'SupplierProfile' && { source_types: ['CustomerProfile', 'SupplierProfile'] }),
        report_type: reportType,
        start_date: dateToDateString(dates.start, '01'),
        is_admin_reports: true,
        react_data: true,
        end_date: dateToDateString(dates.end, new Date(dates.end.getFullYear(), dates.end.getMonth() + 1, 0).getDate()),
        ...(sourceType === 'CustomerProfile' && !!activeCustomer && { customer_id: activeCustomer.id }),
        ...(sourceType === 'SupplierProfile' && !!activeSupplier && { supplier_id: activeSupplier.id }),
        ...(sourceType === 'SupplierProfile' && !!excludeStaffing && { exclude_staffing: true }),
      },
    });
    setLoading(false);
    setReport(data);
    setReportData(data.report_data);
  }, [dates, sourceType, activeCustomer, activeSupplier, reportType, excludeStaffing]);

  const promiseOptionsCustomers = debounce(async (query) => {
    // For company admins (non-admin users), show all accessible users by default
    // For admin users, require at least 3 characters to search
    if (isAdmin && (!query || query.length < 3)) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: {
        ...(query && { query }),
        // For company admins, get all customers they have access to
        ...(!isAdmin && { limit: 50 }), // Reasonable limit for dropdown
      },
    });

    return responseCustomers.map((customer) => ({
      value: customer.id,
      label: customer.name,
      id: customer.id,
      email: customer.email,
    }));
  }, 1000);

  const promiseOptionsSuppliers = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseSuppliers } = await axios({
      method: 'GET',
      url: apiAdminSuppliersPath(),
      params: { query },
    });

    return responseSuppliers.map((supplier) => ({
      value: supplier.id,
      label: supplier.name,
      id: supplier.id,
      email: supplier.email,
    }));
  }, 1000);

  return (
    <>
      <div className="dashboard-filters reports-filters between-flex" style={{ marginRight: 0 }}>
        {isAdmin && (
          <div className="between-flex list-toggle">
            <span
              className={`admin-order-toggle ${sourceType === 'CustomerProfile' ? 'active' : ''}`}
              onClick={() => {
                setActiveSupplier(null);
                setSourceType('CustomerProfile');
              }}
            >
              Customer Report
            </span>
            <span
              className={`admin-order-toggle ${sourceType === 'SupplierProfile' ? 'active' : ''}`}
              onClick={() => {
                setActiveCustomer(null);
                setSourceType('SupplierProfile');
              }}
            >
              Supplier Report
            </span>
          </div>
        )}
        {!isAdmin && <span>Spends Report</span>}
        {loading && <Spinner bgColor="black" />}
        <div className="between-flex">
          <div className="dashboard-filter dashboard-filter__datepicker">
            <span className="between-flex">For Dates: </span>
            <div className="between-flex">
              <DatePicker
                startDate={dates.start}
                endDate={dates.end}
                selected={dates.start}
                onChange={(newDates) => onDateChange(newDates)}
                dateFormat="MM/yyyy"
                showMonthYearPicker
                selectsRange
                className="dashboard-filter"
              />
            </div>
          </div>
          {sourceType === 'CustomerProfile' && (
            <AsyncSelect
              className="form-input reports-search no-border"
              cacheOptions
              defaultOptions={!isAdmin} // Load default options for company admins
              isClearable
              placeholder={isAdmin ? '🔍 Search Customers' : 'Select Team Member'}
              loadOptions={promiseOptionsCustomers}
              onChange={(selected) => setActiveCustomer(selected)}
              value={activeCustomer}
              styles={{
                control: (baseStyles) => ({
                  ...baseStyles,
                  zIndex: 5,
                  height: '37px',
                  minHeight: '37px',
                  borderRadius: '18.5px',
                  display: 'flex',
                  alignItems: 'center',
                  boxShadow: '0 2px 5px 1px #403c4329',
                  border: 'none',
                }),
                valueContainer: (baseStyles) => ({
                  ...baseStyles,
                  height: '35px',
                  padding: '0 16px',
                  display: 'flex',
                  alignItems: 'center',
                }),
                input: (baseStyles) => ({
                  ...baseStyles,
                  margin: '0',
                  padding: '0',
                }),
                indicatorsContainer: (baseStyles) => ({
                  ...baseStyles,
                  height: '35px',
                  paddingRight: '8px',
                }),
                indicatorSeparator: () => ({
                  display: 'none',
                }),
                dropdownIndicator: (baseStyles) => ({
                  ...baseStyles,
                  padding: '4px',
                }),
                clearIndicator: (baseStyles) => ({
                  ...baseStyles,
                  padding: '4px',
                }),
              }}
            />
          )}
          {sourceType === 'SupplierProfile' && (
            <AsyncSelect
              className="form-input reports-search"
              cacheOptions
              defaultOptions
              isClearable
              placeholder="Search Suppliers"
              loadOptions={promiseOptionsSuppliers}
              onChange={(selected) => setActiveSupplier(selected)}
              value={activeSupplier}
              styles={{
                control: (baseStyles) => ({
                  ...baseStyles,
                  zIndex: 5,
                  height: '37px',
                  minHeight: '37px',
                  borderRadius: '18.5px', // Half of height for pill shape
                  display: 'flex',
                  alignItems: 'center',
                }),
                valueContainer: (baseStyles) => ({
                  ...baseStyles,
                  height: '35px',
                  padding: '0 16px',
                  display: 'flex',
                  alignItems: 'center',
                }),
                input: (baseStyles) => ({
                  ...baseStyles,
                  margin: '0',
                  padding: '0',
                }),
                indicatorsContainer: (baseStyles) => ({
                  ...baseStyles,
                  height: '35px',
                  paddingRight: '8px',
                }),
                indicatorSeparator: () => ({
                  display: 'none',
                }),
                dropdownIndicator: (baseStyles) => ({
                  ...baseStyles,
                  padding: '4px',
                }),
                clearIndicator: (baseStyles) => ({
                  ...baseStyles,
                  padding: '4px',
                }),
              }}
            />
          )}
        </div>
      </div>
      {activeCustomer && (
        <p>
          Data fitered to customer: <strong>{activeCustomer.label}</strong>
        </p>
      )}
      {activeSupplier && (
        <p>
          Data fitered to supplier: <strong>{activeSupplier.label}</strong>
        </p>
      )}
      {!!reportData.length && sourceType === 'CustomerProfile' && (
        <div className="reporting">
          <ReportsGraph reportData={reportData} type={sourceType} loading={loading} />
          <ReportDoughnut report={report} title="category" fields={['catering', 'snacks']} />
          <ReportDoughnut report={report} title="supplier" fields={['ethical', 'suppliers']} />
        </div>
      )}
      {!!reportData.length && sourceType === 'SupplierProfile' && (
        <>
          <div style={{ display: 'flex' }}>
            <div className="report-period-options">
              <label style={{ marginRight: '1rem' }}>
                <input
                  type="radio"
                  name="reportType"
                  value="monthly"
                  checked={reportType === 'monthly'}
                  onChange={() => setReportType('monthly')}
                />
                Monthly
              </label>
              <label>
                <input
                  type="radio"
                  name="reportType"
                  value="weekly"
                  checked={reportType === 'weekly'}
                  onChange={() => setReportType('weekly')}
                />
                Weekly
              </label>
            </div>
            <label style={{ marginLeft: '1rem' }}>
              <input type="checkbox" checked={excludeStaffing} onChange={() => setExcludeStaffing(!excludeStaffing)} />
              Exclude Staffing Spends
            </label>
          </div>
          <ReportsGraph reportData={reportData} type={sourceType} loading={loading} />
        </>
      )}
      <div>
        {!reportData.length && !loading && sourceType !== 'Pantry Manager Spends' && <h2>No data available</h2>}
      </div>
    </>
  );
};

export default ReportsAdminApp;
